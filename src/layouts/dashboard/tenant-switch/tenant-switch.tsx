import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Avatar from "@mui/material/Avatar";
import IconButton from "@mui/material/IconButton";
import SvgIcon from "@mui/material/SvgIcon";
import Tooltip from "@mui/material/Tooltip";
import ChevronDownIcon from "@untitled-ui/icons-react/build/esm/ChevronDown";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { useRouter } from "next/router";

import { useShop } from "@/src/api/hooks/shop/use-shop";
import { usePopover } from "src/hooks/use-popover";
import { useStoreId } from "@/src/hooks/use-store-id";
import { logger } from "@/src/utils/logger";
import { StorageService } from "nextjs-api-lib";
import { paths } from "@/src/paths";
import { log } from "console";
import { useAppSelector } from "@/src/redux/hooks";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

export const TenantSwitch = (props) => {
  const { isCollapsed = false, ...otherProps } = props;
  const [shops, setShops] = useState<any[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const popover = usePopover();
  const { getShop, detailShop } = useShop();
  const storeId = useStoreId();
  const router = useRouter();

  useEffect(() => {
    const fetchListShop = async () => {
      const res = await getShop(0, 10000);
      if (res?.status === 200) {
        setShops(res?.data);
      }
    };
    fetchListShop();
  }, []);
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDetailsClick = async (storeId: string) => {
    try {
      // const response = await detailShop(storeId);
      // setShopInfo(response.data);
      // StorageService.set('shopInfo', JSON.stringify(response.data));
      StorageService.set("currentStoreId", storeId);
      window.location.reload();
    } catch (error) {
      logger.error("Error fetching shop info:", error);
    }
  };

  return (
    <>
      {isCollapsed ? (
        // Collapsed mode: chỉ hiển thị avatar có thể click
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
          }}
          {...otherProps}
        >
          <Tooltip title={currentShop?.shopName || "Chọn shop"} placement="right" arrow>
            <IconButton
              onClick={handleMenuOpen}
              sx={{
                p: 0,
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
            >
              <Avatar
                src={currentShop?.shopLogo?.link}
                sx={{
                  height: 40,
                  width: 40,
                  border: "2px solid transparent",
                  "&:hover": {
                    border: "2px solid #2654FE",
                  },
                }}
              />
            </IconButton>
          </Tooltip>
        </Box>
      ) : (
        // Expanded mode: hiển thị đầy đủ như cũ
        <Stack alignItems="center" direction="row" spacing={2} {...otherProps}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexGrow: 1 }}>
            <Avatar
              src={currentShop?.shopLogo?.link}
              sx={{
                height: 40,
                width: 40,
              }}
            />
            <Stack sx={{ gap: 0.5, width: 100 }}>
              <TruncatedText
                typographyProps={{ fontSize: 16, color: "#000", fontWeight: 600 }}
                text={currentShop?.shopName}
              />
              <TruncatedText
                typographyProps={{ fontSize: 14, color: "#000" }}
                text={currentShop?.shopSlogan}
              />
            </Stack>
          </Box>
          <IconButton onClick={handleMenuOpen} sx={{ cursor: "pointer" }}>
            <SvgIcon>
              <ChevronDownIcon />
            </SvgIcon>
          </IconButton>
        </Stack>
      )}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        sx={{ zIndex: 9999999 }}
      >
        {Array.isArray(shops) &&
          shops.map((shop) => (
            <MenuItem key={shop.shopId} onClick={() => handleDetailsClick(shop.shopId)}>
              <ListItemIcon>
                <Avatar src={shop.shopLogo?.link} sx={{ height: 40, width: 40, mr: 1 }} />
              </ListItemIcon>
              <ListItemText
                primary={shop.shopName}
                secondary={shop.shopSlogan}
                slotProps={{
                  primary: {
                    noWrap: true,
                    sx: { maxWidth: "200px" },
                  },
                  secondary: {
                    noWrap: true,
                    sx: { maxWidth: "200px" },
                  },
                }}
              />
            </MenuItem>
          ))}
      </Menu>
    </>
  );
};
