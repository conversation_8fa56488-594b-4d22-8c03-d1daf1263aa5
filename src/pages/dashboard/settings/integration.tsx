import React, { useEffect, useState } from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Avatar,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Chip,
  Link,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormControl,
  FormLabel,
  Tooltip,
} from "@mui/material";
import IntegrationInstructionsIcon from "@mui/icons-material/IntegrationInstructions";
import StoreIcon from "@mui/icons-material/Store";
import InfoIcon from "@mui/icons-material/Info";
import { useRouter } from "next/router";
import IntegrationForm, { IntegrationField } from "./components/IntegrationForm";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSyncService } from "@/src/api/hooks/sync-service/sync-service";
export const SyncService = {
  Sapo: "Sapo",
  KiotViet: "KiotViet",
  NhanhVN: "Nhan<PERSON><PERSON>",
  Odoo: "Odoo",
};
const integrations = [
  {
    key: "nhanh",
    name: "Nhanh.vn",
    description: "Kết nối với Nhan<PERSON>.vn để đồng bộ đơn hàng, tồn kho, ...",
    icon: <img style={{ borderRadius: "50%" }} src="/assets/nhanh.png" alt="nhanh" />,
    color: "#e3f2fd",
    fields: [
      { name: "appId", label: "App ID", placeholder: "Nhập App ID", required: true },
      {
        name: "secretKey",
        label: "Secret Key",
        placeholder: "Nhập Secret Key",
        type: "password",
        required: true,
      },
    ],
  },
  {
    key: "kiotviet",
    name: "KiotViet",
    description: "Kết nối với KiotViet để đồng bộ dữ liệu bán hàng.",
    icon: <StoreIcon sx={{ fontSize: 40, color: "#43a047" }} />,
    color: "#e8f5e9",
    fields: [
      { name: "clientId", label: "Client ID", placeholder: "Nhập Client ID", required: true },
      {
        name: "clientSecret",
        label: "Client Secret",
        placeholder: "Nhập Client Secret",
        type: "password",
        required: true,
      },
    ],
  },
  {
    key: "Sapo",
    name: "Sapo",
    description: "Kết nối với Sapo để đồng bộ đơn hàng, tồn kho, ...",
    icon: (
      <img
        style={{ borderRadius: "50%" }}
        src="https://moitruongtest1.mysapogo.com/favicon.ico"
        alt="sapo"
      />
    ),
    color: "#e3f2fd",
    fields: [
      {
        name: "domainApi",
        label: "Tên cửa hàng Sapo",
        placeholder: "Nhập Tên cửa hàng Sapo",
        required: true,
      },
      {
        name: "clientId",
        label: "Client ID",
        placeholder: "Nhập Client ID",
        required: true,
      },
      {
        name: "clientSecret",
        label: "Client Secret",
        placeholder: "Nhập Client Secret",
        type: "password",
        required: true,
      },
    ],
  },
];

const mockConnected = {
  nhanh: false,
  kiotviet: false,
  Sapo: false,
};

const LeftColumn = ({ children }) => (
  <Grid item xs={12} md={4}>
    <Box>{children}</Box>
  </Grid>
);

const RightColumn = ({ children }) => (
  <Grid item xs={12} md={8} sx={{ padding: 2 }}>
    <Box>{children}</Box>
  </Grid>
);

const IntegrationList: React.FC = () => {
  const storeId = useStoreId();
  const [openDialog, setOpenDialog] = useState<string | null>(null);
  const [connected, setConnected] = useState(mockConnected);
  const [openDevDialog, setOpenDevDialog] = useState(false);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openSapoDetailDialog, setOpenSapoDetailDialog] = useState(false);
  const [openSapoConfirmDialog, setOpenSapoConfirmDialog] = useState(false);
  const [openKiotVietDetailDialog, setOpenKiotVietDetailDialog] = useState(false);
  const [openKiotVietConfirmDialog, setOpenKiotVietConfirmDialog] = useState(false);
  const [openWarningDialog, setOpenWarningDialog] = useState(false);
  const [warningMessage, setWarningMessage] = useState("");
  const {
    configSyncService,
    getSyncServiceConfig,
    updateAccessCode,
    deleteSyncServiceConfig,
    loading,
  } = useSyncService();
  const [nhanhConfig, setNhanhConfig] = useState<Record<string, string> | null>(null);
  const [sapoConfig, setSapoConfig] = useState<Record<string, string> | null>(null);
  const [kiotVietConfig, setKiotVietConfig] = useState<Record<string, string> | null>(null);
  const router = useRouter();

  useEffect(() => {
    const { accessCode } = router.query;
    if (accessCode && storeId) {
      (async () => {
        try {
          await updateAccessCode({ accessCode, ShopId: storeId, syncService: SyncService.NhanhVN });
          await fetchNhanhConfig();
        } catch (error) {
          console.error("Lưu accessCode thất bại:", error);
        } finally {
          const { accessCode, ...restQuery } = router.query;
          router.replace({ pathname: router.pathname, query: restQuery }, undefined, {
            shallow: true,
          });
        }
      })();
    }
  }, [router.query.accessCode, storeId]);

  useEffect(() => {
    if (storeId) {
      fetchNhanhConfig();
      fetchSapoConfig();
      fetchKiotVietConfig();
    }
  }, [storeId]);

  const fetchNhanhConfig = async () => {
    try {
      const response = await getSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.NhanhVN,
      });
      console.log(response);
      if (response.data.result && response.data.result.status == "Actived") {
        setConnected((prev) => ({
          ...prev,
          nhanh: !!response?.data.result,
        }));
        setNhanhConfig({
          appId: response.data.result.appId || "",
          secretKey: response.data.result.secretKey || "",
          verifyToken: response.data.result.verifyToken || "",
        });
      } else {
        setNhanhConfig(null);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
    }
  };

  const fetchSapoConfig = async () => {
    if (!storeId) return;
    try {
      const response = await getSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.Sapo,
      });
      if (response.data.result && response.data.result.status === "Actived") {
        setConnected((prev) => ({
          ...prev,
          Sapo: true,
        }));
        setSapoConfig({
          domainApi: response.data.result.additionalConfig || "",
          clientId: response.data.result.appId || "",
          clientSecret: response.data.result.clientSecret || "",
        });
      } else {
        setConnected((prev) => ({
          ...prev,
          Sapo: false,
        }));
        setSapoConfig(null);
      }
    } catch (error) {
      console.error("Error fetching Sapo config:", error);
      setConnected((prev) => ({
        ...prev,
        Sapo: false,
      }));
      setSapoConfig(null);
    }
  };

  const fetchKiotVietConfig = async () => {
    if (!storeId) return;
    try {
      const response = await getSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.KiotViet,
      });
      if (response.data.result && response.data.result.status === "Actived") {
        setConnected((prev) => ({
          ...prev,
          kiotviet: true,
        }));
        setKiotVietConfig({
          clientId: response.data.result.appId || "",
          clientSecret: response.data.result.secretKey || "",
          domainApi: response.data.result.additionalConfig || "",
        });
      } else {
        setConnected((prev) => ({
          ...prev,
          kiotviet: false,
        }));
        setKiotVietConfig(null);
      }
    } catch (error) {
      console.error("Error fetching KiotViet config:", error);
      setConnected((prev) => ({
        ...prev,
        kiotviet: false,
      }));
      setKiotVietConfig(null);
    }
  };

  // Kiểm tra xem có dịch vụ nào đang kết nối không
  const getConnectedService = () => {
    return Object.keys(connected).find((service) => connected[service]);
  };

  const handleOpenDialog = (key: string) => {
    if (key === "kiotviet") {
      if (connected.kiotviet) {
        setOpenKiotVietDetailDialog(true);
      } else {
        const connectedService = getConnectedService();
        if (connectedService && connectedService !== key) {
          setWarningMessage(
            `Bạn cần ngắt kết nối dịch vụ ${getServiceName(
              connectedService
            )} trước khi kết nối dịch vụ khác.`
          );
          setOpenWarningDialog(true);
          return;
        }
        setOpenDialog(key);
      }
    } else if (key === "nhanh" && connected.nhanh) {
      setOpenDetailDialog(true);
    } else if (key === "Sapo" && connected.Sapo) {
      setOpenSapoDetailDialog(true);
    } else {
      // Kiểm tra xem có dịch vụ nào đang kết nối không
      const connectedService = getConnectedService();
      if (connectedService && connectedService !== key) {
        // Hiển thị dialog cảnh báo yêu cầu ngắt kết nối dịch vụ hiện tại
        setWarningMessage(
          `Bạn cần ngắt kết nối dịch vụ ${getServiceName(
            connectedService
          )} trước khi khi kết nối dịch vụ khác.`
        );
        setOpenWarningDialog(true);
        return;
      }
      setOpenDialog(key);
    }
  };

  const getServiceName = (key: string) => {
    const service = updatedIntegrations.find((item) => item.key === key);
    return service ? service.name : key;
  };
  const handleCloseDialog = () => setOpenDialog(null);
  const handleCloseDevDialog = () => setOpenDevDialog(false);
  const handleCloseDetailDialog = () => setOpenDetailDialog(false);
  const handleCloseSapoDetailDialog = () => setOpenSapoDetailDialog(false);
  const handleCloseKiotVietDetailDialog = () => setOpenKiotVietDetailDialog(false);

  const updatedIntegrations = [
    {
      key: "nhanh",
      name: "Nhanh.vn",
      description: "Kết nối với Nhanh.vn để đồng bộ đơn hàng, tồn kho, ...",
      icon: <img style={{ borderRadius: "50%" }} src="/assets/nhanh.png" alt="nhanh" />,
      color: "#e3f2fd",
      fields: [
        { name: "appId", label: "App ID", placeholder: "Nhập App ID", required: true },
        {
          name: "secretKey",
          label: "Secret Key",
          placeholder: "Nhập Secret Key",
          type: "password",
          required: true,
        },
      ],
    },
    {
      key: "kiotviet",
      name: "KiotViet",
      description: "Kết nối với KiotViet để đồng bộ dữ liệu bán hàng.",
      icon: <StoreIcon sx={{ fontSize: 40, color: "#43a047" }} />,
      color: "#e8f5e9",
      fields: [
        {
          name: "domainApi",
          label: "Tên cửa hàng KiotViet",
          placeholder: "Nhập Tên cửa hàng KiotViet",
          required: true,
        },
        { name: "clientId", label: "Client ID", placeholder: "Nhập Client ID", required: true },
        {
          name: "clientSecret",
          label: "Client Secret",
          placeholder: "Nhập Client Secret",
          type: "password",
          required: true,
        },
      ],
    },
    {
      key: "Sapo",
      name: "Sapo",
      description: "Kết nối với Sapo để đồng bộ đơn hàng, tồn kho, ...",
      icon: (
        <img
          style={{ borderRadius: "50%" }}
          src="https://moitruongtest1.mysapogo.com/favicon.ico"
          alt="sapo"
        />
      ),
      color: "#e3f2fd",
      fields: [
        {
          name: "domainApi",
          label: "Tên cửa hàng Sapo",
          placeholder: "Nhập Tên cửa hàng Sapo",
          required: true,
        },
        {
          name: "clientId",
          label: "Client ID",
          placeholder: "Nhập Client ID",
          required: true,
        },
        {
          name: "clientSecret",
          label: "Client Secret",
          placeholder: "Nhập Client Secret",
          type: "password",
          required: true,
        },
      ],
    },
  ];

  const handleSubmit = async (key: string, values: Record<string, string>) => {
    if (key === "nhanh") {
      try {
        const response = await configSyncService({
          ...values,
          ShopId: storeId,
          syncService: SyncService.NhanhVN,
        });
        if (response.data.result.data) {
          const appId = response.data.result.data.appId;
          const returnLink = `${window.location.origin}/dashboard/settings/integration`;
          const oauthUrl = `https://nhanh.vn/oauth?version=2.0&appId=${appId}&returnLink=${encodeURIComponent(
            returnLink
          )}`;
          window.location.href = oauthUrl;
        }
      } catch (error) {
        console.error(error);
      }
    } else if (key === "Sapo") {
      try {
        const submitData = {
          ...values,
          ShopId: storeId,
          syncService: SyncService.Sapo,
          connectionType: "oauth",
        };

        await configSyncService(submitData);
        await fetchSapoConfig();
        handleCloseDialog();
      } catch (error) {
        console.error("Lỗi khi kết nối Sapo:", error);
      }
    } else if (key === "kiotviet") {
      try {
        const submitData = {
          ...values,
          ShopId: storeId,
          syncService: SyncService.KiotViet,
        };

        await configSyncService(submitData);
        await fetchKiotVietConfig();
        handleCloseDialog();
      } catch (error) {
        console.error("Lỗi khi kết nối KiotViet:", error);
      }
    } else {
      setConnected((prev) => ({ ...prev, [key]: true }));
      setOpenDialog(null);
    }
  };

  const handleDisconnectNhanh = () => {
    setOpenConfirmDialog(true);
  };

  const handleConfirmDisconnect = async () => {
    try {
      await deleteSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.NhanhVN,
      });
      setConnected((prev) => ({ ...prev, nhanh: false }));
      setNhanhConfig(null);
      setOpenDetailDialog(false);
      setOpenConfirmDialog(false);
    } catch (error) {
      console.error("Lỗi khi ngừng kết nối:", error);
    }
  };

  const handleDisconnectSapo = () => {
    setOpenSapoConfirmDialog(true);
  };

  const handleConfirmDisconnectSapo = async () => {
    try {
      await deleteSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.Sapo,
      });
      setConnected((prev) => ({ ...prev, Sapo: false }));
      setSapoConfig(null);
      setOpenSapoDetailDialog(false);
      setOpenSapoConfirmDialog(false);
    } catch (error) {
      console.error("Lỗi khi ngừng kết nối Sapo:", error);
    }
  };

  // Thêm disconnect handlers cho KiotViet
  const handleDisconnectKiotViet = () => {
    setOpenKiotVietConfirmDialog(true);
  };

  const handleConfirmDisconnectKiotViet = async () => {
    try {
      await deleteSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.KiotViet,
      });
      setConnected((prev) => ({ ...prev, kiotviet: false }));
      setKiotVietConfig(null);
      setOpenKiotVietDetailDialog(false);
      setOpenKiotVietConfirmDialog(false);
    } catch (error) {
      console.error("Lỗi khi ngừng kết nối KiotViet:", error);
    }
  };

  return (
    <SettingLayout>
      <Grid container spacing={4}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={2}>
            Dịch vụ tích hợp
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Kết nối các dịch vụ bên thứ ba để đồng bộ dữ liệu, đơn hàng, tồn kho và nhiều hơn nữa.
            Chọn dịch vụ bên phải để thiết lập kết nối.
          </Typography>
        </LeftColumn>
        <RightColumn>
          <Typography fontWeight="bold" mb={1}>
            Danh sách dịch vụ tích hợp
          </Typography>
          <Box display="flex" flexDirection="column" gap={2}>
            {updatedIntegrations.map((item) => {
              const isConnected = connected[item.key];
              const connectedService = getConnectedService();
              const canConnect = !connectedService || connectedService === item.key;
              return (
                <Paper
                  key={item.key}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    p: 2,
                    border: "1px solid #e0e0e0",
                    boxShadow: 0,
                    background: item.color,
                  }}
                >
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 2,
                      border: `3px solid #e0e0e0`,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                      background: "#f5f5f5",
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Box flex={1} minWidth={0}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography fontWeight={700}>{item.name}</Typography>
                      <Box
                        sx={{
                          backgroundColor: isConnected ? "#00ce00" : "#f1c232",
                          color: "#fff",
                          borderRadius: 1,
                          px: 1,
                          fontSize: 12,
                          fontWeight: 600,
                          lineHeight: "20px",
                          display: "inline-block",
                        }}
                      >
                        {isConnected ? "Đã kết nối" : "Chưa kết nối"}
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {item.description}
                    </Typography>
                  </Box>
                  <Tooltip
                    title={
                      !isConnected && !canConnect
                        ? `Bạn cần ngắt kết nối dịch vụ ${getServiceName(
                            connectedService
                          )} trước khi kết nối dịch vụ khác`
                        : ""
                    }
                    arrow
                  >
                    <span>
                      <Button
                        variant="contained"
                        onClick={() => handleOpenDialog(item.key)}
                        sx={{ fontWeight: 600, minWidth: 140, ml: 2 }}
                        disabled={!isConnected && !canConnect}
                      >
                        {isConnected ? "Xem chi tiết" : "Thiết lập"}
                      </Button>
                    </span>
                  </Tooltip>
                  {item.key !== "kiotviet" && (
                    <Dialog
                      open={openDialog === item.key}
                      onClose={handleCloseDialog}
                      maxWidth="xs"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 4,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                          p: 0,
                          overflow: "visible",
                        },
                      }}
                    >
                      <Box
                        sx={{
                          p: { xs: 2, sm: 4 },
                          pt: 0,
                          bgcolor: "#f8fbff",
                          borderRadius: 4,
                          minWidth: { xs: 320, sm: 400 },
                          maxWidth: 480,
                          position: "relative",
                        }}
                      >
                        <Box
                          display="flex"
                          flexDirection="column"
                          alignItems="center"
                          mt={-6}
                          mb={2}
                        >
                          <Box
                            sx={{
                              width: 80,
                              height: 80,
                              borderRadius: "50%",
                              bgcolor: "#fff",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              boxShadow: "0 4px 16px 0 rgba(25, 118, 210, 0.15)",
                              border: "3px solid #e3f2fd",
                            }}
                          >
                            {item.icon}
                          </Box>
                          <Typography variant="h6" fontWeight={700} mt={2}>
                            Kết nối {item.name}
                          </Typography>
                        </Box>

                        {item.key === "Sapo" && (
                          <Box mb={2} textAlign="center">
                            <Typography variant="body2" color="text.secondary">
                              Chi tiết cách lấy thông tin kết nối{" "}
                              <Link
                                href="https://sapogo.dev/docs/Authentication/oauth"
                                target="_blank"
                                rel="noopener noreferrer"
                                underline="hover"
                                sx={{ fontWeight: 500, color: "primary.main" }}
                              >
                                Xem tài liệu Sapo OAuth
                              </Link>
                            </Typography>
                          </Box>
                        )}

                        <IntegrationForm
                          title=""
                          fields={item.fields}
                          onSubmit={(values) => handleSubmit(item.key, values)}
                          loading={loading}
                          successMessage={`Lưu thông tin kết nối ${item.name} thành công!`}
                          values={
                            item.key === "nhanh"
                              ? nhanhConfig
                              : item.key === "Sapo"
                              ? sapoConfig
                              : undefined
                          }
                        />
                      </Box>
                    </Dialog>
                  )}
                  {/* Dialog cho KiotViet */}
                  {item.key === "kiotviet" && (
                    <Dialog
                      open={openDialog === "kiotviet"}
                      onClose={handleCloseDialog}
                      maxWidth="xs"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 4,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                          p: 0,
                          overflow: "visible",
                        },
                      }}
                    >
                      <Box
                        sx={{
                          p: { xs: 2, sm: 4 },
                          pt: 0,
                          bgcolor: "#f8fbff",
                          borderRadius: 4,
                          minWidth: { xs: 320, sm: 400 },
                          maxWidth: 480,
                          position: "relative",
                        }}
                      >
                        <Box
                          display="flex"
                          flexDirection="column"
                          alignItems="center"
                          mt={-6}
                          mb={2}
                        >
                          <Box
                            sx={{
                              width: 80,
                              height: 80,
                              borderRadius: "50%",
                              bgcolor: "#fff",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              boxShadow: "0 4px 16px 0 rgba(25, 118, 210, 0.15)",
                              border: "3px solid #e8f5e9",
                            }}
                          >
                            <StoreIcon sx={{ fontSize: 40, color: "#43a047" }} />
                          </Box>
                          <Typography variant="h6" fontWeight={700} mt={2}>
                            Kết nối KiotViet
                          </Typography>
                        </Box>

                        <IntegrationForm
                          title=""
                          fields={item.fields}
                          onSubmit={(values) => handleSubmit(item.key, values)}
                          loading={loading}
                          successMessage={`Lưu thông tin kết nối ${item.name} thành công!`}
                          values={kiotVietConfig}
                        />
                      </Box>
                    </Dialog>
                  )}
                  {item.key === "nhanh" && (
                    <Dialog
                      open={openDetailDialog}
                      onClose={handleCloseDetailDialog}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 3,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                        },
                      }}
                    >
                      <DialogTitle sx={{ pb: 1 }}>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Box
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: "50%",
                              bgcolor: "#e3f2fd",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            {item.icon}
                          </Box>
                          <Box>
                            <Typography variant="h6" fontWeight={700}>
                              Chi tiết kết nối Nhanh.vn
                            </Typography>
                            <Chip
                              label="Đã kết nối"
                              color="success"
                              size="small"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        </Box>
                      </DialogTitle>
                      <Divider />
                      <DialogContent sx={{ pt: 3 }}>
                        <Box display="flex" flexDirection="column" gap={3}>
                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              App ID
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {nhanhConfig?.appId || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Secret Key
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {"*".repeat(nhanhConfig?.secretKey?.length || 0)}
                            </Typography>
                          </Box>

                          <Box>
                            <Box display="flex" alignItems="center" gap={1} mb={1}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                color="text.secondary"
                              >
                                Webhooks Verify Token
                              </Typography>
                              <InfoIcon sx={{ fontSize: 16, color: "text.secondary" }} />
                            </Box>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              mb={1}
                              display="block"
                            >
                              Token này được sử dụng để xác thực webhook từ Nhanh.vn. Nhập token này
                              vào cấu hình webhook bên Nhanh.vn
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {nhanhConfig?.verifyToken || "Chưa có token"}
                            </Typography>
                          </Box>
                        </Box>
                      </DialogContent>
                      <DialogActions sx={{ p: 3, pt: 2, justifyContent: "end" }}>
                        <Box display="flex" gap={1}>
                          <Button onClick={handleCloseDetailDialog} variant="outlined">
                            Đóng
                          </Button>
                          <Button
                            onClick={handleDisconnectNhanh}
                            variant="outlined"
                            color="error"
                            disabled={loading}
                          >
                            Ngừng kết nối
                          </Button>
                        </Box>
                      </DialogActions>
                    </Dialog>
                  )}

                  {item.key === "Sapo" && (
                    <Dialog
                      open={openSapoDetailDialog}
                      onClose={handleCloseSapoDetailDialog}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 3,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                        },
                      }}
                    >
                      <DialogTitle sx={{ pb: 1 }}>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Box
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: "50%",
                              bgcolor: "#e3f2fd",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            {item.icon}
                          </Box>
                          <Box>
                            <Typography variant="h6" fontWeight={700}>
                              Chi tiết kết nối Sapo
                            </Typography>
                            <Chip
                              label="Đã kết nối"
                              color="success"
                              size="small"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        </Box>
                      </DialogTitle>
                      <Divider />
                      <DialogContent sx={{ pt: 3 }}>
                        <Box display="flex" flexDirection="column" gap={3}>
                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Domain API
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {sapoConfig?.domainApi || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Client ID
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {sapoConfig?.clientId || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Client Secret
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                                color: "text.secondary",
                              }}
                            >
                              ••••••••••••••••••••••••••••••••
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ mt: 0.5, display: "block" }}
                            >
                              Client Secret đã được ẩn để bảo mật
                            </Typography>
                          </Box>
                        </Box>
                      </DialogContent>
                      <DialogActions sx={{ p: 3, pt: 2, justifyContent: "end" }}>
                        <Box display="flex" gap={1}>
                          <Button onClick={handleCloseSapoDetailDialog} variant="outlined">
                            Đóng
                          </Button>
                          <Button
                            onClick={handleDisconnectSapo}
                            variant="outlined"
                            color="error"
                            disabled={loading}
                          >
                            Ngừng kết nối
                          </Button>
                        </Box>
                      </DialogActions>
                    </Dialog>
                  )}

                  {/* Thêm dialog chi tiết cho KiotViet */}
                  {item.key === "kiotviet" && (
                    <Dialog
                      open={openKiotVietDetailDialog}
                      onClose={handleCloseKiotVietDetailDialog}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 3,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                        },
                      }}
                    >
                      <DialogTitle sx={{ pb: 1 }}>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Box
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: "50%",
                              bgcolor: "#e8f5e9",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <StoreIcon sx={{ fontSize: 32, color: "#43a047" }} />
                          </Box>
                          <Box>
                            <Typography variant="h6" fontWeight={700}>
                              Chi tiết kết nối KiotViet
                            </Typography>
                            <Chip
                              label="Đã kết nối"
                              color="success"
                              size="small"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        </Box>
                      </DialogTitle>
                      <Divider />
                      <DialogContent sx={{ pt: 3 }}>
                        <Box display="flex" flexDirection="column" gap={3}>
                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Tên cửa hàng KiotViet
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {kiotVietConfig?.domainApi || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Client ID
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {kiotVietConfig?.clientId || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Client Secret
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                                color: "text.secondary",
                              }}
                            >
                              ••••••••••••••••••••••••••••••••
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ mt: 0.5, display: "block" }}
                            >
                              Client Secret đã được ẩn để bảo mật
                            </Typography>
                          </Box>
                        </Box>
                      </DialogContent>
                      <DialogActions sx={{ p: 3, pt: 2, justifyContent: "end" }}>
                        <Box display="flex" gap={1}>
                          <Button onClick={handleCloseKiotVietDetailDialog} variant="outlined">
                            Đóng
                          </Button>
                          <Button
                            onClick={handleDisconnectKiotViet}
                            variant="outlined"
                            color="error"
                            disabled={loading}
                          >
                            Ngừng kết nối
                          </Button>
                        </Box>
                      </DialogActions>
                    </Dialog>
                  )}
                </Paper>
              );
            })}
          </Box>
        </RightColumn>
      </Grid>

      {/* Dialog xác nhận ngừng kết nối Nhanh.vn */}
      <Dialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight={700}>
            Xác nhận ngừng kết nối
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="text.secondary">
            Bạn có chắc chắn muốn ngừng kết nối với Nhanh.vn? Tất cả cấu hình và dữ liệu kết nối sẽ
            bị xóa.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setOpenConfirmDialog(false)} variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmDisconnect}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Ngừng kết nối
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openSapoConfirmDialog}
        onClose={() => setOpenSapoConfirmDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight={700}>
            Xác nhận ngừng kết nối
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="text.secondary">
            Bạn có chắc chắn muốn ngừng kết nối với Sapo? Tất cả cấu hình và dữ liệu kết nối sẽ bị
            xóa.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setOpenSapoConfirmDialog(false)} variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmDisconnectSapo}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Ngừng kết nối
          </Button>
        </DialogActions>
      </Dialog>

      {/* Thêm dialog xác nhận ngừng kết nối KiotViet */}
      <Dialog
        open={openKiotVietConfirmDialog}
        onClose={() => setOpenKiotVietConfirmDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight={700}>
            Xác nhận ngừng kết nối
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="text.secondary">
            Bạn có chắc chắn muốn ngừng kết nối với KiotViet? Tất cả cấu hình và dữ liệu kết nối sẽ
            bị xóa.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setOpenKiotVietConfirmDialog(false)} variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmDisconnectKiotViet}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Ngừng kết nối
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog cảnh báo khi muốn kết nối dịch vụ khác */}
      <Dialog
        open={openWarningDialog}
        onClose={() => setOpenWarningDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight={700}>
            Không thể kết nối
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="text.secondary">{warningMessage}</Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setOpenWarningDialog(false)} variant="contained">
            Đã hiểu
          </Button>
        </DialogActions>
      </Dialog>
    </SettingLayout>
  );
};

export default IntegrationList;
