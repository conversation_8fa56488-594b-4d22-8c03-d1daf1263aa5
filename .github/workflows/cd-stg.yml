name: Build and Deploy Next.js to Server Staging

on:
  push:
    branches:
      - staging
      - test-ci2
  workflow_dispatch:

jobs:
  build:
    name: Build
    # if: github.ref == 'refs/heads/deploy'
    runs-on: [self-hosted, stg]
    env:
      NODE_OPTIONS: '--max_old_space_size=2048'

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Cache node_modules and yarn cache
        uses: actions/cache@v4
        id: yarn-cache
        with:
          path: |
            node_modules
            ~/.cache/yarn
          key: yarn-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies only if cache missed
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn install

      - name: Build
        run: |
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          yarn build:staging
          sudo cp -r build/. /var/www/admin-partner/build

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build AdminPartner STG success - ${{ github.event.head_commit.message }}'
