name: Build and Deploy Next.js to Server Testing

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    name: Build
    # if: github.ref == 'refs/heads/deploy'
    runs-on: [self-hosted, dev]
    env:
      CI: true
      SOURCE_DIR: /var/www/admin-partner

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Export PATH
        run: echo "$HOME/.yarn/bin:$HOME/.config/yarn/global/node_modules/.bin" >> $GITHUB_PATH

      - name: Use Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      
      - name: Cache node_modules and yarn cache
        uses: actions/cache@v4
        id: yarn-cache
        with:
          path: |
            node_modules
            ~/.cache/yarn
          key: yarn-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies only if cache missed
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn install

      - name: Install dependencies
        run: |
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          yarn build:development
          sudo cp -r build/. /var/www/admin-partner/build

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build AdminPartner Dev success - ${{ github.event.head_commit.message }}'
